import pygame
import random

# 初始化 Pygame
pygame.init()

# 颜色定义
BLACK = (0, 0, 0)
WHITE = (255, 255, 255)
GREEN = (0, 200, 0)  # 稍微调整绿色
DARK_GREEN = (0, 100, 0)  # 深绿色，用于蛇的身体
RED = (200, 0, 0) # 稍微调整红色
BRIGHT_RED = (255, 0, 0) #亮红色

# 屏幕尺寸
SCREEN_WIDTH = 600
SCREEN_HEIGHT = 480

# 蛇的方块大小
BLOCK_SIZE = 20

# 游戏速度
SNAKE_SPEED = 15  # 稍微加快速度

# 创建屏幕
screen = pygame.display.set_mode((SCREEN_WIDTH, SCREEN_HEIGHT))
pygame.display.set_caption("贪吃蛇")

# 时钟
clock = pygame.time.Clock()

# 字体
font_style = pygame.font.SysFont("comicsansms", 25)  # 使用一个更常见的字体
score_font = pygame.font.SysFont("comicsansms", 35)

def Your_score(score):
    value = score_font.render("Your Score: " + str(score), True, WHITE)
    screen.blit(value, [0, 0])

def our_snake(block_size, snake_list):
    for i, x in enumerate(snake_list):
        if i == len(snake_list) - 1:  # 蛇头使用亮绿色
            pygame.draw.rect(screen, GREEN, [x[0], x[1], block_size, block_size])
        else:  # 蛇身使用深绿色
            pygame.draw.rect(screen, DARK_GREEN, [x[0], x[1], block_size, block_size])


def message(msg, color):
    mesg = font_style.render(msg, True, color)
    text_rect = mesg.get_rect(center=(SCREEN_WIDTH / 2, SCREEN_HEIGHT / 2)) #居中显示
    screen.blit(mesg, text_rect)

def game_loop():
    game_over = False
    game_close = False

    # 蛇的初始位置
    x1 = SCREEN_WIDTH / 2
    y1 = SCREEN_HEIGHT / 2

    # 蛇的移动速度
    x1_change = 0
    y1_change = 0

    # 蛇的身体
    snake_List = []
    snake_Length = 1

    # 随机生成食物的位置
    foodx = round(random.randrange(0, SCREEN_WIDTH - BLOCK_SIZE) / BLOCK_SIZE) * BLOCK_SIZE
    foody = round(random.randrange(0, SCREEN_HEIGHT - BLOCK_SIZE) / BLOCK_SIZE) * BLOCK_SIZE

    while not game_over:

        while game_close == True:
            screen.fill(BLACK)
            message("你输了! 按 C-重新开始 或 Q-退出", RED)
            Your_score(snake_Length - 1)
            pygame.display.update()

            for event in pygame.event.get():
                if event.type == pygame.KEYDOWN:
                    if event.key == pygame.K_q:
                        game_over = True
                        game_close = False
                    if event.key == pygame.K_c:
                        game_loop()

        for event in pygame.event.get():
            if event.type == pygame.QUIT:
                game_over = True
            if event.type == pygame.KEYDOWN:
                if event.key == pygame.K_LEFT:
                    x1_change = -BLOCK_SIZE
                    y1_change = 0
                elif event.key == pygame.K_RIGHT:
                    x1_change = BLOCK_SIZE
                    y1_change = 0
                elif event.key == pygame.K_UP:
                    y1_change = -BLOCK_SIZE
                    x1_change = 0
                elif event.key == pygame.K_DOWN:
                    y1_change = BLOCK_SIZE
                    x1_change = 0

        if x1 >= SCREEN_WIDTH or x1 < 0 or y1 >= SCREEN_HEIGHT or y1 < 0:
            game_close = True

        x1 += x1_change
        y1 += y1_change

        screen.fill(BLACK)
        pygame.draw.rect(screen, BRIGHT_RED, [foodx, foody, BLOCK_SIZE, BLOCK_SIZE]) #亮红色食物
        snake_Head = []
        snake_Head.append(x1)
        snake_Head.append(y1)
        snake_List.append(snake_Head)

        if len(snake_List) > snake_Length:
            del snake_List[0]

        for x in snake_List[:-1]:
            if x == snake_Head:
                game_close = True

        our_snake(BLOCK_SIZE, snake_List)
        Your_score(snake_Length - 1) #显示得分

        pygame.display.update()

        if x1 == foodx and y1 == foody:
            foodx = round(random.randrange(0, SCREEN_WIDTH - BLOCK_SIZE) / BLOCK_SIZE) * BLOCK_SIZE
            foody = round(random.randrange(0, SCREEN_HEIGHT - BLOCK_SIZE) / BLOCK_SIZE) * BLOCK_SIZE
            snake_Length += 1

        clock.tick(SNAKE_SPEED)

    pygame.quit()
    quit()

# 启动游戏
game_loop()