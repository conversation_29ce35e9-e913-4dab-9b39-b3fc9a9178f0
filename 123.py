import pygame
import sys
import random
import time

# 常量
BOARD_SIZE = 15
CELL_SIZE = 40
GRID_SIZE = CELL_SIZE
WIDTH = BOARD_SIZE * CELL_SIZE
HEIGHT = BOARD_SIZE * CELL_SIZE + 50  # 增加底部空间显示状态和按钮
BLACK = (0, 0, 0)
WHITE = (255, 255, 255)
GRAY = (128, 128, 128)
YELLOW = (255, 255, 0)
RED = (255, 0, 0)

# 棋盘状态：0为空，1为黑子，2为白子
board = [[0 for _ in range(BOARD_SIZE)] for _ in range(BOARD_SIZE)]

# 玩家：1为黑子，2为白子 (AI)
current_player = 1

# 历史记录 - 用于悔棋
move_history = []

# AI思考状态
ai_thinking = False
ai_start_time = 0
ai_time_used = 0

# Pygame初始化
pygame.init()
screen = pygame.display.set_mode((WIDTH, HEIGHT))
pygame.display.set_caption("五子棋")
font = pygame.font.SysFont(None, 24)

def draw_board():
    """绘制棋盘"""
    screen.fill(GRAY)
    for i in range(BOARD_SIZE):
        pygame.draw.line(screen, BLACK, (GRID_SIZE // 2, GRID_SIZE // 2 + i * GRID_SIZE), (WIDTH - GRID_SIZE // 2, GRID_SIZE // 2 + i * GRID_SIZE))
        pygame.draw.line(screen, BLACK, (GRID_SIZE // 2 + i * GRID_SIZE, GRID_SIZE // 2), (GRID_SIZE // 2 + i * GRID_SIZE, HEIGHT - GRID_SIZE // 2 - 50))
    
    # 绘制悔棋按钮
    pygame.draw.rect(screen, WHITE, (WIDTH // 2 - 50, HEIGHT - 40, 100, 30))
    undo_text = font.render("悔棋", True, BLACK)
    screen.blit(undo_text, (WIDTH // 2 - 20, HEIGHT - 35))
    
    # 显示状态信息
    if ai_thinking:
        status_text = font.render(f"AI思考中... {time.time() - ai_start_time:.1f}秒", True, RED)
    else:
        if current_player == 1:
            status_text = font.render("轮到你下棋(黑子)", True, BLACK)
        else:
            status_text = font.render(f"AI思考了 {ai_time_used:.1f}秒", True, BLACK)
    
    screen.blit(status_text, (10, HEIGHT - 35))

def draw_pieces():
    """绘制棋子"""
    for row in range(BOARD_SIZE):
        for col in range(BOARD_SIZE):
            if board[row][col] == 1:
                pygame.draw.circle(screen, BLACK, (GRID_SIZE // 2 + col * GRID_SIZE, GRID_SIZE // 2 + row * GRID_SIZE), CELL_SIZE // 2 - 2)
            elif board[row][col] == 2:
                pygame.draw.circle(screen, WHITE, (GRID_SIZE // 2 + col * GRID_SIZE, GRID_SIZE // 2 + row * GRID_SIZE), CELL_SIZE // 2 - 2)

def place_piece(row, col, player):
    """放置棋子"""
    if board[row][col] == 0:
        board[row][col] = player
        move_history.append((row, col, player))
        return True
    return False

def undo_move():
    """悔棋功能"""
    global current_player
    
    if len(move_history) >= 2:  # 需要撤销玩家和AI的两步
        # 撤销AI的一步
        ai_row, ai_col, _ = move_history.pop()
        board[ai_row][ai_col] = 0
        
        # 撤销玩家的一步
        player_row, player_col, _ = move_history.pop()
        board[player_row][player_col] = 0
        
        # 重置为玩家回合
        current_player = 1
        return True
    elif len(move_history) == 1:  # 只有玩家走了一步，AI还没走
        player_row, player_col, _ = move_history.pop()
        board[player_row][player_col] = 0
        current_player = 1
        return True
    
    return False

def check_win(row, col, player):
    """检查输赢"""
    # 检查水平方向
    count = 0
    for i in range(max(0, col - 4), min(BOARD_SIZE, col + 5)):
        if board[row][i] == player:
            count += 1
            if count == 5:
                return True
        else:
            count = 0

    # 检查垂直方向
    count = 0
    for i in range(max(0, row - 4), min(BOARD_SIZE, row + 5)):
        if board[i][col] == player:
            count += 1
            if count == 5:
                return True
        else:
            count = 0

    # 检查正斜线方向
    count = 0
    for i in range(-4, 5):
        r = row + i
        c = col + i
        if 0 <= r < BOARD_SIZE and 0 <= c < BOARD_SIZE:
            if board[r][c] == player:
                count += 1
                if count == 5:
                    return True
            else:
                count = 0

    # 检查反斜线方向
    count = 0
    for i in range(-4, 5):
        r = row + i
        c = col - i
        if 0 <= r < BOARD_SIZE and 0 <= c < BOARD_SIZE:
            if board[r][c] == player:
                count += 1
                if count == 5:
                    return True
            else:
                count = 0

    return False

def evaluate_board(board, player):
    """更高级的棋盘评估函数"""
    opponent = 3 - player
    score = 0

    # 评估函数权重 (可以根据实际效果调整)
    FIVE_WEIGHT = 100000  # 五连
    FOUR_WEIGHT = 10000   # 活四
    BLOCKED_FOUR_WEIGHT = 1000 # 冲四/死四
    THREE_WEIGHT = 100     # 活三
    BLOCKED_THREE_WEIGHT = 50  # 冲三
    TWO_WEIGHT = 10        # 活二
    BLOCKED_TWO_WEIGHT = 5   # 冲二

    # 检查每个方向上的连子情况
    for row in range(BOARD_SIZE):
        for col in range(BOARD_SIZE):
            # 水平方向
            score += evaluate_line(board, row, col, 0, 1, player, opponent,
                                     FIVE_WEIGHT, FOUR_WEIGHT, BLOCKED_FOUR_WEIGHT,
                                     THREE_WEIGHT, BLOCKED_THREE_WEIGHT, TWO_WEIGHT,
                                     BLOCKED_TWO_WEIGHT)
            # 垂直方向
            score += evaluate_line(board, row, col, 1, 0, player, opponent,
                                     FIVE_WEIGHT, FOUR_WEIGHT, BLOCKED_FOUR_WEIGHT,
                                     THREE_WEIGHT, BLOCKED_THREE_WEIGHT, TWO_WEIGHT,
                                     BLOCKED_TWO_WEIGHT)
            # 正斜线方向
            score += evaluate_line(board, row, col, 1, 1, player, opponent,
                                     FIVE_WEIGHT, FOUR_WEIGHT, BLOCKED_FOUR_WEIGHT,
                                     THREE_WEIGHT, BLOCKED_THREE_WEIGHT, TWO_WEIGHT,
                                     BLOCKED_TWO_WEIGHT)
            # 反斜线方向
            score += evaluate_line(board, row, col, 1, -1, player, opponent,
                                     FIVE_WEIGHT, FOUR_WEIGHT, BLOCKED_FOUR_WEIGHT,
                                     THREE_WEIGHT, BLOCKED_THREE_WEIGHT, TWO_WEIGHT,
                                     BLOCKED_TWO_WEIGHT)
    return score

def evaluate_line(board, row, col, row_increment, col_increment, player, opponent,
                  FIVE_WEIGHT, FOUR_WEIGHT, BLOCKED_FOUR_WEIGHT,
                  THREE_WEIGHT, BLOCKED_THREE_WEIGHT, TWO_WEIGHT,
                  BLOCKED_TWO_WEIGHT):
    """评估一条线上的连子情况"""
    score = 0
    line = []
    for i in range(5):
        r = row + i * row_increment
        c = col + i * col_increment
        if 0 <= r < BOARD_SIZE and 0 <= c < BOARD_SIZE:
            line.append(board[r][c])
        else:
            line.append(-1)  # 超出边界

    # 统计连子情况
    player_count = line.count(player)
    opponent_count = line.count(opponent)

    if player_count > 0 and opponent_count == 0:  # 只考虑对player有利的情况
        if player_count == 5:
            score += FIVE_WEIGHT
        elif player_count == 4:
            # 检查是否为活四 (两端为空)
            if is_live_four(board, row, col, row_increment, col_increment, player):
                score += FOUR_WEIGHT
            else:
                score += BLOCKED_FOUR_WEIGHT  # 冲四/死四
        elif player_count == 3:
            # 检查是否为活三
            if is_live_three(board, row, col, row_increment, col_increment, player):
                score += THREE_WEIGHT
            else:
                score += BLOCKED_THREE_WEIGHT  # 冲三
        elif player_count == 2:
            # 检查是否为活二
            if is_live_two(board, row, col, row_increment, col_increment, player):
                score += TWO_WEIGHT
            else:
                score += BLOCKED_TWO_WEIGHT  # 冲二

    return score

def is_live_four(board, row, col, row_increment, col_increment, player):
    """判断是否为活四"""
    # 检查两端是否为空
    r_start = row - row_increment
    c_start = col - col_increment
    r_end = row + 4 * row_increment
    c_end = col + 4 * col_increment

    is_live = True

    if 0 <= r_start < BOARD_SIZE and 0 <= c_start < BOARD_SIZE:
        if board[r_start][c_start] != 0:
            is_live = False
    else:
        is_live = False # 超出边界，不算活四

    if 0 <= r_end < BOARD_SIZE and 0 <= c_end < BOARD_SIZE:
        if board[r_end][c_end] != 0:
            is_live = False
    else:
        is_live = False # 超出边界，不算活四

    return is_live

def is_live_three(board, row, col, row_increment, col_increment, player):
    """判断是否为活三 (更复杂，需要考虑更多情况)"""
    # 简化判断，只判断两端是否为空，实际情况更复杂
    r_start = row - row_increment
    c_start = col - col_increment
    r_end = row + 3 * row_increment
    c_end = col + 3 * col_increment

    is_live = True

    if 0 <= r_start < BOARD_SIZE and 0 <= c_start < BOARD_SIZE:
        if board[r_start][c_start] != 0:
            is_live = False
    else:
        is_live = False

    if 0 <= r_end < BOARD_SIZE and 0 <= c_end < BOARD_SIZE:
        if board[r_end][c_end] != 0:
            is_live = False
    else:
        is_live = False

    return is_live

def is_live_two(board, row, col, row_increment, col_increment, player):
    """判断是否为活二 (简化判断)"""
    r_start = row - row_increment
    c_start = col - col_increment
    r_end = row + 2 * row_increment
    c_end = col + 2 * col_increment

    is_live = True

    if 0 <= r_start < BOARD_SIZE and 0 <= c_start < BOARD_SIZE:
        if board[r_start][c_start] != 0:
            is_live = False
    else:
        is_live = False

    if 0 <= r_end < BOARD_SIZE and 0 <= c_end < BOARD_SIZE:
        if board[r_end][c_end] != 0:
            is_live = False
    else:
        is_live = False

    return is_live

def get_valid_moves(board):
    """获取有效的移动位置 (只搜索有棋子周围的位置)"""
    moves = set()
    for row in range(BOARD_SIZE):
        for col in range(BOARD_SIZE):
            if board[row][col] != 0:
                # 搜索周围一圈的位置
                for i in range(-1, 2):
                    for j in range(-1, 2):
                        r = row + i
                        c = col + j
                        if 0 <= r < BOARD_SIZE and 0 <= c < BOARD_SIZE and board[r][c] == 0:
                            moves.add((r, c))
    if not moves: # 如果没有棋子，则返回中心点
        moves.add((BOARD_SIZE // 2, BOARD_SIZE // 2))
    return list(moves)

def minimax(board, depth, maximizing_player, alpha, beta, player):
    """Minimax算法 with Alpha-Beta剪枝 (优化搜索空间)"""
    opponent = 3 - player
    if depth == 0 or game_is_over(board):
        return evaluate_board(board, player), None

    valid_moves = get_valid_moves(board)

    if maximizing_player:
        max_eval = float('-inf')
        best_move = None
        for row, col in valid_moves: # 只遍历有效的移动位置
            board[row][col] = player
            eval, _ = minimax(board, depth - 1, False, alpha, beta, player)
            board[row][col] = 0  # 撤销移动
            if eval > max_eval:
                max_eval = eval
                best_move = (row, col)
            alpha = max(alpha, eval)
            if beta <= alpha:
                break  # Beta剪枝
        return max_eval, best_move
    else:
        min_eval = float('inf')
        best_move = None
        for row, col in valid_moves: # 只遍历有效的移动位置
            board[row][col] = opponent
            eval, _ = minimax(board, depth - 1, True, alpha, beta, player)
            board[row][col] = 0  # 撤销移动
            if eval < min_eval:
                min_eval = eval
                best_move = (row, col)
            beta = min(beta, eval)
            if beta <= alpha:
                break  # Alpha剪枝
        return min_eval, best_move

def game_is_over(board):
    """检查游戏是否结束 (棋盘满了或者有玩家获胜)"""
    # 检查是否棋盘已满
    for row in range(BOARD_SIZE):
        for col in range(BOARD_SIZE):
            if board[row][col] == 0:
                return False  # 还有空位，游戏未结束

    # 检查是否有玩家获胜
    for row in range(BOARD_SIZE):
        for col in range(BOARD_SIZE):
            if check_win(row, col, 1) or check_win(row, col, 2):
                return True

    return True  # 棋盘已满，游戏结束 (平局)

def ai_move_iterative_deepening(board, player, max_time=3):
    """使用迭代加深的AI移动"""
    global ai_time_used
    
    best_move = None
    start_time = time.time()
    depth = 1
    while time.time() - start_time < max_time:
        _, move = minimax(board, depth, True, float('-inf'), float('inf'), player)
        if move:
            best_move = move
        else:
            break  # 没有找到移动，可能游戏结束
        depth += 1
    if best_move:
        row, col = best_move
        if place_piece(row, col, player):
            ai_time_used = time.time() - start_time
            return row, col
    return None, None

def game_loop():
    """游戏主循环"""
    global current_player, ai_thinking, ai_start_time
    game_over = False
    winner = None

    while True:
        # 处理事件
        for event in pygame.event.get():
            if event.type == pygame.QUIT:
                pygame.quit()
                sys.exit()

            # 玩家回合且点击事件
            if event.type == pygame.MOUSEBUTTONDOWN and not game_over:
                x, y = event.pos
                
                # 检查是否点击了悔棋按钮
                if WIDTH // 2 - 50 <= x <= WIDTH // 2 + 50 and HEIGHT - 40 <= y <= HEIGHT - 10:
                    if undo_move():
                        # 悔棋成功，重置游戏状态
                        game_over = False
                        winner = None
                        ai_thinking = False
                # 否则尝试放置棋子
                elif current_player == 1 and y < HEIGHT - 50:
                    col = (x - GRID_SIZE // 2) // GRID_SIZE
                    row = (y - GRID_SIZE // 2) // GRID_SIZE

                    if 0 <= row < BOARD_SIZE and 0 <= col < BOARD_SIZE:
                        if place_piece(row, col, current_player):
                            # 立即重绘棋盘，显示玩家落子
                            draw_board()
                            draw_pieces()
                            pygame.display.flip()
                            
                            if check_win(row, col, current_player):
                                winner = "黑子"
                                game_over = True
                            else:
                                current_player = 2  # 轮到AI
                                ai_thinking = True
                                ai_start_time = time.time()

        # AI回合
        if current_player == 2 and not game_over and ai_thinking:
            # 在AI思考时实时更新思考时间显示
            current_time = time.time()
            if current_time - ai_start_time < 0.1:  # 控制刷新率
                draw_board()
                draw_pieces()
                pygame.display.flip()
                continue
                
            row, col = ai_move_iterative_deepening(board, 2, 3)  # AI使用迭代加深，最多搜索3秒
            ai_thinking = False
            
            if row is not None:
                if check_win(row, col, current_player):
                    winner = "白子(AI)"
                    game_over = True
                else:
                    current_player = 1  # 轮到玩家

        # 绘制游戏状态
        draw_board()
        draw_pieces()
        
        # 显示游戏结果
        if game_over and winner:
            result_text = font.render(f"{winner}胜!", True, RED)
            screen.blit(result_text, (WIDTH // 2 - 40, 10))
        
        pygame.display.flip()
        pygame.time.Clock().tick(30)  # 控制帧率

# 启动游戏
if __name__ == "__main__":
    game_loop()