import tkinter as tk
from tkinter import messagebox
import numpy as np
import random
import time

class GomokuGame:
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("五子棋游戏")
        self.root.resizable(False, False)
        
        # 游戏参数
        self.board_size = 15
        self.cell_size = 30
        self.margin = 30
        
        # 游戏状态
        self.board = np.zeros((self.board_size, self.board_size), dtype=int)
        self.current_player = 1  # 1为黑子，2为白子
        self.game_over = False
        self.ai_mode = False  # AI模式开关
        self.ai_player = 2  # AI默认为白子
        self.thinking = False  # AI思考状态
        
        # 创建画布
        canvas_size = self.board_size * self.cell_size + 2 * self.margin
        self.canvas = tk.Canvas(
            self.root, 
            width=canvas_size, 
            height=canvas_size + 50,
            bg='#DEB887'
        )
        self.canvas.pack()
        
        # 状态标签
        self.status_label = tk.Label(
            self.root, 
            text="黑子先行", 
            font=('Arial', 14),
            bg='#DEB887'
        )
        self.status_label.pack()
        
        # 按钮框架
        button_frame = tk.Frame(self.root, bg='#DEB887')
        button_frame.pack(pady=5)

        # 重新开始按钮
        self.restart_button = tk.Button(
            button_frame,
            text="重新开始",
            font=('Arial', 12),
            command=self.restart_game
        )
        self.restart_button.pack(side=tk.LEFT, padx=5)

        # AI模式切换按钮
        self.ai_button = tk.Button(
            button_frame,
            text="开启AI",
            font=('Arial', 12),
            command=self.toggle_ai_mode
        )
        self.ai_button.pack(side=tk.LEFT, padx=5)
        
        # 绑定鼠标点击事件
        self.canvas.bind("<Button-1>", self.on_click)
        
        # 绘制棋盘
        self.draw_board()
        
    def draw_board(self):
        """绘制棋盘"""
        self.canvas.delete("all")
        
        # 绘制网格线
        for i in range(self.board_size):
            # 垂直线
            x = self.margin + i * self.cell_size
            self.canvas.create_line(
                x, self.margin,
                x, self.margin + (self.board_size - 1) * self.cell_size,
                fill='black', width=1
            )
            
            # 水平线
            y = self.margin + i * self.cell_size
            self.canvas.create_line(
                self.margin, y,
                self.margin + (self.board_size - 1) * self.cell_size, y,
                fill='black', width=1
            )
        
        # 绘制天元和星位
        star_positions = [3, 7, 11]
        for row in star_positions:
            for col in star_positions:
                x = self.margin + col * self.cell_size
                y = self.margin + row * self.cell_size
                self.canvas.create_oval(
                    x - 3, y - 3, x + 3, y + 3,
                    fill='black'
                )
        
        # 绘制已下的棋子
        for row in range(self.board_size):
            for col in range(self.board_size):
                if self.board[row][col] != 0:
                    self.draw_stone(row, col, self.board[row][col])
    
    def draw_stone(self, row, col, player):
        """绘制棋子"""
        x = self.margin + col * self.cell_size
        y = self.margin + row * self.cell_size
        radius = self.cell_size // 2 - 2
        
        color = 'black' if player == 1 else 'white'
        outline = 'black'
        
        self.canvas.create_oval(
            x - radius, y - radius,
            x + radius, y + radius,
            fill=color, outline=outline, width=2
        )
    
    def toggle_ai_mode(self):
        """切换AI模式"""
        self.ai_mode = not self.ai_mode
        if self.ai_mode:
            self.ai_button.config(text="关闭AI")
            self.status_label.config(text="AI模式：黑子(玩家) vs 白子(AI)")
        else:
            self.ai_button.config(text="开启AI")
            self.status_label.config(text="双人模式：黑子先行")
        self.restart_game()

    def on_click(self, event):
        """处理鼠标点击事件"""
        if self.game_over or self.thinking:
            return
        
        # 计算点击的格子坐标
        col = round((event.x - self.margin) / self.cell_size)
        row = round((event.y - self.margin) / self.cell_size)
        
        # 检查坐标是否有效
        if (0 <= row < self.board_size and 
            0 <= col < self.board_size and 
            self.board[row][col] == 0):
            
            # 下棋
            self.board[row][col] = self.current_player
            self.draw_stone(row, col, self.current_player)
            
            # 检查胜负
            if self.check_winner(row, col):
                winner = "黑子" if self.current_player == 1 else "白子"
                self.status_label.config(text=f"{winner}获胜！")
                self.game_over = True
                messagebox.showinfo("游戏结束", f"{winner}获胜！")
            elif np.all(self.board != 0):
                self.status_label.config(text="平局！")
                self.game_over = True
                messagebox.showinfo("游戏结束", "平局！")
            else:
                # 切换玩家
                self.current_player = 3 - self.current_player
                if self.ai_mode and self.current_player == self.ai_player:
                    self.status_label.config(text="AI思考中...")
                    self.thinking = True
                    # 延迟执行AI移动，让界面有时间更新
                    self.root.after(500, self.ai_move)
                else:
                    player_name = "黑子" if self.current_player == 1 else "白子"
                    if self.ai_mode:
                        self.status_label.config(text="轮到玩家(黑子)")
                    else:
                        self.status_label.config(text=f"轮到{player_name}")
    
    def check_winner(self, row, col):
        """检查是否有玩家获胜"""
        directions = [
            (0, 1),   # 水平
            (1, 0),   # 垂直
            (1, 1),   # 主对角线
            (1, -1)   # 副对角线
        ]
        
        player = self.board[row][col]
        
        for dr, dc in directions:
            count = 1  # 包含当前棋子
            
            # 向一个方向检查
            r, c = row + dr, col + dc
            while (0 <= r < self.board_size and 
                   0 <= c < self.board_size and 
                   self.board[r][c] == player):
                count += 1
                r += dr
                c += dc
            
            # 向相反方向检查
            r, c = row - dr, col - dc
            while (0 <= r < self.board_size and 
                   0 <= c < self.board_size and 
                   self.board[r][c] == player):
                count += 1
                r -= dr
                c -= dc
            
            # 如果连成5子则获胜
            if count >= 5:
                return True
        
        return False
    
    def restart_game(self):
        """重新开始游戏"""
        self.board = np.zeros((self.board_size, self.board_size), dtype=int)
        self.current_player = 1
        self.game_over = False
        self.thinking = False
        if self.ai_mode:
            self.status_label.config(text="AI模式：轮到玩家(黑子)")
        else:
            self.status_label.config(text="黑子先行")
        self.draw_board()
    
    def ai_move(self):
        """AI移动"""
        if self.game_over:
            return

        # 获取最佳移动位置
        row, col = self.get_best_move()

        if row is not None and col is not None:
            # 执行AI移动
            self.board[row][col] = self.ai_player
            self.draw_stone(row, col, self.ai_player)

            # 检查AI是否获胜
            if self.check_winner(row, col):
                self.status_label.config(text="AI获胜！")
                self.game_over = True
                messagebox.showinfo("游戏结束", "AI获胜！")
            elif np.all(self.board != 0):
                self.status_label.config(text="平局！")
                self.game_over = True
                messagebox.showinfo("游戏结束", "平局！")
            else:
                # 切换回玩家
                self.current_player = 3 - self.current_player
                self.status_label.config(text="轮到玩家(黑子)")

        self.thinking = False

    def get_best_move(self):
        """获取AI的最佳移动"""
        # 获取所有可能的移动位置
        possible_moves = self.get_possible_moves()

        if not possible_moves:
            return None, None

        best_score = float('-inf')
        best_move = None

        # 评估每个可能的移动
        for row, col in possible_moves:
            # 模拟移动
            self.board[row][col] = self.ai_player
            score = self.minimax(3, False, float('-inf'), float('inf'))
            self.board[row][col] = 0  # 撤销移动

            if score > best_score:
                best_score = score
                best_move = (row, col)

        return best_move

    def get_possible_moves(self):
        """获取所有可能的移动位置（在已有棋子周围）"""
        moves = set()

        # 如果棋盘为空，返回中心位置
        if np.all(self.board == 0):
            center = self.board_size // 2
            return [(center, center)]

        # 在已有棋子周围寻找空位
        for row in range(self.board_size):
            for col in range(self.board_size):
                if self.board[row][col] != 0:
                    # 检查周围8个方向
                    for dr in [-1, 0, 1]:
                        for dc in [-1, 0, 1]:
                            if dr == 0 and dc == 0:
                                continue
                            nr, nc = row + dr, col + dc
                            if (0 <= nr < self.board_size and
                                0 <= nc < self.board_size and
                                self.board[nr][nc] == 0):
                                moves.add((nr, nc))

        return list(moves)

    def minimax(self, depth, is_maximizing, alpha, beta):
        """Minimax算法与Alpha-Beta剪枝"""
        # 检查游戏结束状态
        winner = self.evaluate_board()
        if winner != 0 or depth == 0:
            return self.get_board_score(winner)

        possible_moves = self.get_possible_moves()

        if is_maximizing:
            max_score = float('-inf')
            for row, col in possible_moves:
                self.board[row][col] = self.ai_player
                score = self.minimax(depth - 1, False, alpha, beta)
                self.board[row][col] = 0

                max_score = max(max_score, score)
                alpha = max(alpha, score)
                if beta <= alpha:
                    break  # Alpha-Beta剪枝
            return max_score
        else:
            min_score = float('inf')
            for row, col in possible_moves:
                self.board[row][col] = 3 - self.ai_player  # 对手
                score = self.minimax(depth - 1, True, alpha, beta)
                self.board[row][col] = 0

                min_score = min(min_score, score)
                beta = min(beta, score)
                if beta <= alpha:
                    break  # Alpha-Beta剪枝
            return min_score

    def evaluate_board(self):
        """评估棋盘状态，返回获胜者"""
        for row in range(self.board_size):
            for col in range(self.board_size):
                if self.board[row][col] != 0:
                    if self.check_winner_at_position(row, col):
                        return self.board[row][col]
        return 0

    def check_winner_at_position(self, row, col):
        """检查指定位置是否形成五子连珠"""
        directions = [(0, 1), (1, 0), (1, 1), (1, -1)]
        player = self.board[row][col]

        for dr, dc in directions:
            count = 1

            # 向一个方向检查
            r, c = row + dr, col + dc
            while (0 <= r < self.board_size and
                   0 <= c < self.board_size and
                   self.board[r][c] == player):
                count += 1
                r += dr
                c += dc

            # 向相反方向检查
            r, c = row - dr, col - dc
            while (0 <= r < self.board_size and
                   0 <= c < self.board_size and
                   self.board[r][c] == player):
                count += 1
                r -= dr
                c -= dc

            if count >= 5:
                return True
        return False

    def get_board_score(self, winner):
        """根据获胜者返回分数"""
        if winner == self.ai_player:
            return 1000
        elif winner == 3 - self.ai_player:
            return -1000
        else:
            return 0

    def run(self):
        """运行游戏"""
        self.root.mainloop()

if __name__ == "__main__":
    game = GomokuGame()
    game.run()
